import { useState, useRef } from 'react';
import * as signalR from '@microsoft/signalr';

export default function Home() {
  const [baseUrl, setBaseUrl] = useState('http://localhost:8082/visual-check');
  const [hubPath, setHubPath] = useState('/hub/status-monitoring');
  const [token, setToken] = useState('');
  const [connected, setConnected] = useState(false);
  const [logs, setLogs] = useState([]);
  const [superiorId, setSuperiorId] = useState('');
  const [shiftInstantId, setShiftInstantId] = useState('');
  const connRef = useRef(null);

  const addLog = (msg) => setLogs((l) => [msg, ...l].slice(0, 500));

  const getHubUrl = () => {
    const base = baseUrl.trim().replace(/\/$/, '');
    const path = hubPath.trim();
    return base + (path.startsWith('/') ? path : '/' + path);
  };

  const connect = async () => {
    if (connRef.current && connRef.current.state === signalR.HubConnectionState.Connected) return;
    const url = getHubUrl();
    const options = token ? { accessTokenFactory: () => token } : {};

    const connection = new signalR.HubConnectionBuilder()
      .withUrl(url, options)
      .withAutomaticReconnect()
      .configureLogging(signalR.LogLevel.Information)
      .build();

    connection.onreconnecting(() => addLog(`${new Date().toLocaleTimeString()} - Reconnecting...`));
    connection.onreconnected(() => addLog(`${new Date().toLocaleTimeString()} - Reconnected`));
    connection.onclose(() => {
      setConnected(false);
      addLog(`${new Date().toLocaleTimeString()} - Disconnected`);
    });

    const eventHandler = (name) => (data) => {
      addLog(`${new Date().toLocaleTimeString()} - ${name}: ${JSON.stringify(data)}`);
    };

    connection.on('OperatorStatusUpdate', eventHandler('OperatorStatusUpdate'));
    connection.on('ShiftInstantStatusUpdate', eventHandler('ShiftInstantStatusUpdate'));

    try {
      await connection.start();
      connRef.current = connection;
      setConnected(true);
      addLog(`${new Date().toLocaleTimeString()} - Connected to ${url}`);
    } catch (err) {
      addLog(`${new Date().toLocaleTimeString()} - Connection error: ${err && err.message ? err.message : err}`);
    }
  };

  const disconnect = async () => {
    if (!connRef.current) return;
    try {
      await connRef.current.stop();
    } catch (err) {
      addLog(`${new Date().toLocaleTimeString()} - Error disconnecting: ${err && err.message ? err.message : err}`);
    } finally {
      connRef.current = null;
      setConnected(false);
    }
  };

  // User group join/leave
  const joinUserGroup = async () => {
    if (!connRef.current || !superiorId) return;
    try {
      await connRef.current.invoke('JoinUserGroup', superiorId);
      addLog(`${new Date().toLocaleTimeString()} - Joined user group: ${superiorId}`);
    } catch (err) {
      addLog(`${new Date().toLocaleTimeString()} - Error joining user group: ${err && err.message ? err.message : err}`);
    }
  };

  const leaveUserGroup = async () => {
    if (!connRef.current || !superiorId) return;
    try {
      await connRef.current.invoke('LeaveUserGroup', superiorId);
      addLog(`${new Date().toLocaleTimeString()} - Left user group: ${superiorId}`);
    } catch (err) {
      addLog(`${new Date().toLocaleTimeString()} - Error leaving user group: ${err && err.message ? err.message : err}`);
    }
  };

  // Shift instant group join/leave
  const joinShiftInstantGroup = async () => {
    if (!connRef.current || !shiftInstantId) return;
    try {
      await connRef.current.invoke('JoinShiftInstantGroup', shiftInstantId);
      addLog(`${new Date().toLocaleTimeString()} - Joined shift instant group: ${shiftInstantId}`);
    } catch (err) {
      addLog(`${new Date().toLocaleTimeString()} - Error joining shift instant group: ${err && err.message ? err.message : err}`);
    }
  };

  const leaveShiftInstantGroup = async () => {
    if (!connRef.current || !shiftInstantId) return;
    try {
      await connRef.current.invoke('LeaveShiftInstantGroup', shiftInstantId);
      addLog(`${new Date().toLocaleTimeString()} - Left shift instant group: ${shiftInstantId}`);
    } catch (err) {
      addLog(`${new Date().toLocaleTimeString()} - Error leaving shift instant group: ${err && err.message ? err.message : err}`);
    }
  };

  return (
    <div style={{ padding: 20, fontFamily: 'Arial, Helvetica, sans-serif' }}>
      <h1>Next.js SignalR Test Client</h1>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 12 }}>
        <div>
          <label style={{ fontWeight: 'bold' }}>Base URL (origin)</label>
          <input value={baseUrl} onChange={(e) => setBaseUrl(e.target.value)} style={{ width: '100%', padding: 8 }} />
        </div>
        <div>
          <label style={{ fontWeight: 'bold' }}>Hub path (relative)</label>
          <input value={hubPath} onChange={(e) => setHubPath(e.target.value)} style={{ width: '100%', padding: 8 }} />
        </div>
      </div>

      <div style={{ marginTop: 10 }}>
        <label style={{ fontWeight: 'bold' }}>Bearer token (optional)</label>
        <textarea value={token} onChange={(e) => setToken(e.target.value)} rows={3} style={{ width: '100%', padding: 8 }} />
      </div>

      <div style={{ marginTop: 10, display: 'flex', gap: 16 }}>
        <div>
          <label style={{ fontWeight: 'bold' }}>Superior ID (User Group)</label>
          <input value={superiorId} onChange={(e) => setSuperiorId(e.target.value)} style={{ width: 120, padding: 6 }} />
          <button onClick={joinUserGroup} disabled={!connected || !superiorId} style={{ marginLeft: 6 }}>Join</button>
          <button onClick={leaveUserGroup} disabled={!connected || !superiorId} style={{ marginLeft: 6 }}>Leave</button>
        </div>
        <div>
          <label style={{ fontWeight: 'bold' }}>Shift Instant ID</label>
          <input value={shiftInstantId} onChange={(e) => setShiftInstantId(e.target.value)} style={{ width: 120, padding: 6 }} />
          <button onClick={joinShiftInstantGroup} disabled={!connected || !shiftInstantId} style={{ marginLeft: 6 }}>Join</button>
          <button onClick={leaveShiftInstantGroup} disabled={!connected || !shiftInstantId} style={{ marginLeft: 6 }}>Leave</button>
        </div>
      </div>

      <div style={{ marginTop: 10 }}>
        <button onClick={connect} disabled={connected} style={{ padding: '8px 12px', marginRight: 8 }}>Connect</button>
        <button onClick={disconnect} disabled={!connected} style={{ padding: '8px 12px' }}>Disconnect</button>
        <span style={{ marginLeft: 12 }}>{connected ? '● Connected' : '○ Disconnected'}</span>
      </div>

      <h3 style={{ marginTop: 20 }}>Logs</h3>
      <div id="logs" style={{ maxHeight: 480, overflow: 'auto', border: '1px solid #ddd', padding: 10, background: '#fafafa' }}>
        {logs.length === 0 && <div style={{ color: '#666' }}>No messages yet</div>}
        {logs.map((l, i) => (
          <pre key={i} style={{ whiteSpace: 'pre-wrap', margin: 0 }}>{l}</pre>
        ))}
      </div>

      <div style={{ marginTop: 12 }}>
        <button onClick={() => setLogs([])}>Clear</button>
      </div>

      <footer style={{ marginTop: 20, color: '#666' }}>
        <div>Port: 3001 (dev)</div>
        <div>Tip: supply a valid JWT in the token field to test per-user messages (Clients.User)</div>
      </footer>
    </div>
  );
}