{"name": "webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 8080", "lint": "next lint", "format:all": "prettier --write \"**/*.{ts,tsx,js,jsx,md,mdx,css,yaml,yml}\"", "check": "prettier --check \"**/*.{ts,tsx,js,jsx,md,mdx,css,yaml,yml}\"", "eslint": "eslint . --ext .js,.jsx,.ts,.tsx", "prepare": "husky", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@microsoft/signalr": "^8.0.7", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-color": "^2.25.0", "@tiptap/extension-font-family": "^2.25.0", "@tiptap/extension-hard-break": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-text-align": "^2.25.0", "@tiptap/extension-text-style": "^2.25.0", "@tiptap/extension-underline": "^2.25.0", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "@xyflow/react": "^12.8.1", "axios": "^1.10.0", "chrono-node": "^2.8.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "cookies-next": "^5.1.0", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "jwt-decode": "^4.0.0", "lint-staged": "^15.5.2", "lucide-react": "^0.469.0", "next": "15.2.4", "next-intl": "^3.26.5", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-hooks": "^1.0.1", "react-tabs": "^6.1.0", "recharts": "2.15.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "^22.16.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/recharts": "^2.0.1", "eslint": "^9.30.1", "eslint-config-next": "15.1.0", "eslint-config-prettier": "^9.1.0", "husky": "^9.1.7", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "postcss": "^8.5.6", "prettier": "3.4.2", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"**/*.{js,ts,jsx,tsx,css,md}": ["eslint --fix", "prettier --write"]}, "engines": {"node": ">=18"}}