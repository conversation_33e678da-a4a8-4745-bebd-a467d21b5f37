import { create } from "zustand";
import api from "@/lib/axios";
import { toast } from "@/hooks/use-toast";
import { signalRService, type OperatorStatusUpdate, type ShiftInstantStatusUpdate, type TeamStatusMonitoringUpdate } from "@/services/signalRService";

// Types
export type AttendanceStatus = "P" | "CTN" | "MA" | "AB" | "REPLACE" | "P_IN_BUS" | "P_IN_PLANT" | "";

export interface TeamOperator {
  id: number;
  mle: string;
  firstName: string;
  lastName: string;
  role: string;
  function: string;
  team: string;
  line?: string;
  status?: AttendanceStatus;
}

export interface TeamApiResponse {
  teamId: string;
  teamName: string;
  operators: TeamOperator[];
  shiftInstantId: string;
  date: string;
}

export interface StatusCode {
  code: string;
  startDateTime: string;
}

export interface DailyRecord {
  date: string;
  statusCodes: StatusCode[];
}

export interface StatusMonitoringOperator {
  id: string;
  legacy_id: number;
  first_name: string;
  last_name: string;
  role: string;
  team_id: string;
  dailyRecords: DailyRecord[];
}

export interface StatusMonitoringResponse {
  last_date: string;
  shift_instant_id: string;
  shift_status: string;
  shift_start: string;
  shift_end: string;
  status_data: StatusMonitoringOperator[];
}

export interface CurrentStatusResponse {
  shiftInstantId: string;
  teamId: string;
  operators: {
    id: number;
    mle: string;
    firstName: string;
    lastName: string;
    role: string;
    function: string;
    team: string;
    line?: string;
    status: AttendanceStatus;
  }[];
}

export interface UpdateStatusResponse {
  success: boolean;
  message: string;
}

export interface StatusInfo {
  id: string;
  statusCode: string;
  statusLabel: string;
  statusName: string;
  type: "INSTANT" | "SCHEDULE";
  color: string;
  createdAt: string;
  updatedAt: string;
}

export interface StatusHistoryEntry {
  date: string;
  status: AttendanceStatus;
  timestamp: string;
  submittedBy: string;
}

export interface StatusHistoryResponse {
  operatorId: number;
  history: StatusHistoryEntry[];
}

// Helper to extract backend error message
interface ErrorDetails {
  error?: { message?: string };
}
interface ErrorResponseData {
  message?: string;
  details?: ErrorDetails;
}
interface AxiosLikeError {
  response?: { data?: ErrorResponseData };
  message?: string;
}

function getApiErrorMessage(error: unknown): string | undefined {
  if (!error) return undefined;
  if (typeof error === "object" && error !== null && "response" in error) {
    const errObj = error as AxiosLikeError;
    const data = errObj.response?.data;
    if (data) {
      if (data.details?.error?.message) return data.details.error.message;
      if (data.message) return data.message;
    }
    if (errObj.message) return errObj.message;
  }
  if (typeof error === "string") return error;
  return undefined;
}

interface TeamLeaderState {
  // Data
  teamData: TeamApiResponse | null;
  currentStatusData: CurrentStatusResponse | null;
  statusMonitoringData: StatusMonitoringResponse | null;
  statusHistory: { [operatorId: number]: StatusHistoryEntry[] };
  statusInfoList: StatusInfo[];

  // Loading states
  isTeamDataLoading: boolean;
  isCurrentStatusLoading: boolean;
  isStatusMonitoringLoading: boolean;
  isStatusHistoryLoading: boolean;
  isUpdatingStatus: boolean;
  isStatusInfoLoading: boolean;

  // Error states
  teamDataError: string | null;
  currentStatusError: string | null;
  statusMonitoringError: string | null;
  statusHistoryError: string | null;
  updateStatusError: string | null;
  statusInfoError: string | null;

  // Actions
  fetchTeamData: (teamId: string, startDate: string) => Promise<void>;
  fetchCurrentStatus: (teamId: string, date: string) => Promise<void>;
  fetchStatusMonitoring: (endDate: string) => Promise<void>;
  fetchStatusHistory: (teamId: string, operatorId: number) => Promise<void>;
  updateOperatorStatus: (
    shiftInstantId: string,
    operatorId: number,
    status: AttendanceStatus,
  ) => Promise<void>;
  updateShiftStatus: (
    shiftInstantId: string,
    status: string,
  ) => Promise<void>;
  updateOperatorAttendanceStatus: (
    shiftInstantId: string,
    operatorId: number,
    statusCode: "P" | "AB",
  ) => Promise<void>;
  fetchStatusInfo: () => Promise<void>;

  // SignalR methods
  connectToStatusMonitoring: () => Promise<void>;
  disconnectFromStatusMonitoring: () => Promise<void>;
  setupStatusMonitoringListeners: () => void;
  joinStatusMonitoringGroups: (teamId?: string, shiftInstantId?: string) => Promise<void>;
  leaveStatusMonitoringGroups: (teamId?: string, shiftInstantId?: string) => Promise<void>;

  // Getters
  getTeamOperators: () => TeamOperator[];
  getCurrentStatusForOperator: (operatorId: number) => AttendanceStatus;
  getStatusHistoryForOperator: (operatorId: number) => StatusHistoryEntry[];
  getStatusMonitoringOperators: () => StatusMonitoringOperator[];

  // Clear actions
  clearTeamData: () => void;
  clearCurrentStatus: () => void;
  clearStatusMonitoring: () => void;
  clearStatusHistory: () => void;
  clearErrors: () => void;
}

export const useTeamLeaderStore = create<TeamLeaderState>((set, get) => ({
  // Initial state
  teamData: null,
  currentStatusData: null,
  statusMonitoringData: null,
  statusHistory: {},
  statusInfoList: [],

  isTeamDataLoading: false,
  isCurrentStatusLoading: false,
  isStatusMonitoringLoading: false,
  isStatusHistoryLoading: false,
  isUpdatingStatus: false,
  isStatusInfoLoading: false,

  teamDataError: null,
  currentStatusError: null,
  statusMonitoringError: null,
  statusHistoryError: null,
  updateStatusError: null,
  statusInfoError: null,

  // Actions
  fetchTeamData: async (teamId: string, startDate: string) => {
    set({ isTeamDataLoading: true, teamDataError: null });

    try {
      const response = await api.get<TeamApiResponse>(
        `/status-tracking/api/teams/${teamId}/shift_instant/history`,
        {
          params: { startDate },
        },
      );

      set({
        teamData: response.data,
        isTeamDataLoading: false,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch team data";
      set({
        teamDataError: errorMessage,
        isTeamDataLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  fetchCurrentStatus: async (teamId: string, date: string) => {
    set({ isCurrentStatusLoading: true, currentStatusError: null });

    try {
      const response = await api.get<CurrentStatusResponse>(
        `/api/teams/${teamId}/current`,
        {
          params: { date },
        },
      );

      set({
        currentStatusData: response.data,
        isCurrentStatusLoading: false,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch current status";
      set({
        currentStatusError: errorMessage,
        isCurrentStatusLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  fetchStatusMonitoring: async (endDate: string) => {
    set({ isStatusMonitoringLoading: true, statusMonitoringError: null });

    try {
      // Calculate start date (5 days before end date)
      const endDateObj = new Date(endDate);
      const startDateObj = new Date(endDateObj);
      startDateObj.setDate(startDateObj.getDate() - 5);
      const startDate = startDateObj.toISOString().split('T')[0];

      const response = await api.get<StatusMonitoringResponse>(
        `/visual-check/api/v1/teamleader/status-monitoring`,
        {
          params: {
            startDate,
            endDate
          },
        },
      );

      set({
        statusMonitoringData: response.data,
        isStatusMonitoringLoading: false,
      });

      // Auto-join status monitoring groups after successful data fetch
      const teamId = response.data.status_data?.[0]?.team_id;
      const shiftInstantId = response.data.shift_instant_id;
      if (teamId || shiftInstantId) {
        get().joinStatusMonitoringGroups(teamId, shiftInstantId);
      }
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch status monitoring data";
      set({
        statusMonitoringError: errorMessage,
        isStatusMonitoringLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  fetchStatusHistory: async (teamId: string, operatorId: number) => {
    set({ isStatusHistoryLoading: true, statusHistoryError: null });

    try {
      const response = await api.get<StatusHistoryResponse>(
        `/api/shift_instant/${teamId}/operators/${operatorId}/status`,
      );

      set((state) => ({
        statusHistory: {
          ...state.statusHistory,
          [operatorId]: response.data.history,
        },
        isStatusHistoryLoading: false,
      }));
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch status history";
      set({
        statusHistoryError: errorMessage,
        isStatusHistoryLoading: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  updateOperatorStatus: async (
    shiftInstantId: string,
    operatorId: number,
    status: AttendanceStatus,
  ) => {
    set({ isUpdatingStatus: true, updateStatusError: null });

    try {
      const response = await api.put<UpdateStatusResponse>(
        `/api/shift_instant/${shiftInstantId}/operators/${operatorId}/status`,
        {
          status,
        },
      );

      if (response.data.success) {
        // Update the current status data with the new status
        set((state) => ({
          currentStatusData: state.currentStatusData
            ? {
                ...state.currentStatusData,
                operators: state.currentStatusData.operators.map((op) =>
                  op.id === operatorId ? { ...op, status } : op,
                ),
              }
            : null,
          isUpdatingStatus: false,
        }));

        toast({
          title: "Success",
          description: "Operator status updated successfully",
        });
      } else {
        throw new Error(response.data.message || "Failed to update status");
      }
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to update operator status";
      set({
        updateStatusError: errorMessage,
        isUpdatingStatus: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  // Getters
  getTeamOperators: () => {
    const { teamData, statusMonitoringData } = get();

    // If we have new status monitoring data, convert it to the old format
    if (statusMonitoringData?.status_data) {
      return statusMonitoringData.status_data.map((operator) => ({
        id: operator.legacy_id,
        mle: operator.legacy_id.toString(),
        firstName: operator.first_name,
        lastName: operator.last_name,
        role: operator.role,
        function: operator.role, 
        team: operator.team_id,
        line: undefined,
        status: undefined, 
      }));
    }

    return teamData?.operators || [];
  },

  getCurrentStatusForOperator: (operatorId: number) => {
    const { currentStatusData, statusMonitoringData } = get();

    // If we have new status monitoring data, get the latest status
    if (statusMonitoringData?.status_data) {
      const operator = statusMonitoringData.status_data.find(
        (op) => op.legacy_id === operatorId,
      );

      if (operator && operator.dailyRecords.length > 0) {
        // Get the latest date's status codes
        const latestRecord = operator.dailyRecords[operator.dailyRecords.length - 1];
        if (latestRecord.statusCodes.length > 0) {
          // Get the most recent status code
          const latestStatus = latestRecord.statusCodes[latestRecord.statusCodes.length - 1];
          return latestStatus.code as AttendanceStatus;
        }
      }
    }

    // Fallback to old current status data
    const operator = currentStatusData?.operators.find(
      (op) => op.id === operatorId,
    );
    return operator?.status || "";
  },

  getStatusHistoryForOperator: (operatorId: number) => {
    const { statusHistory } = get();
    return statusHistory[operatorId] || [];
  },

  getStatusMonitoringOperators: () => {
    const { statusMonitoringData } = get();
    return statusMonitoringData?.status_data || [];
  },

  updateShiftStatus: async (shiftInstantId: string, status: string) => {
    set({ isUpdatingStatus: true, updateStatusError: null });

    try {
      const response = await api.post<UpdateStatusResponse>(
        `/visual-check/api/v1/teamleader/shift_instant/${shiftInstantId}/statuS`,
        {
          shiftStatus: status,
        },
      );

      if (response.data.success) {
        // Update the status monitoring data with the new shift status
        set((state) => ({
          statusMonitoringData: state.statusMonitoringData
            ? {
                ...state.statusMonitoringData,
                shift_status: status,
              }
            : null,
          isUpdatingStatus: false,
        }));

        toast({
          title: "Success",
          description: "Shift status updated successfully",
        });
      } else {
        throw new Error(response.data.message || "Failed to update shift status");
      }
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to update shift status";
      set({
        updateStatusError: errorMessage,
        isUpdatingStatus: false,
      });

      // Don't show toast here, let the component handle it for better error messages
      // Re-throw the error so the component can handle it
      throw error;
    }
  },

  updateOperatorAttendanceStatus: async (
    shiftInstantId: string,
    operatorId: number,
    statusCode: "P" | "AB",
  ) => {
    set({ isUpdatingStatus: true, updateStatusError: null });

    try {
      const response = await api.post<UpdateStatusResponse>(
        `/visual-check/api/v1/teamleader/shift_instant/${shiftInstantId}/operators/${operatorId}/statuS`,
        {
          statusCode,
        },
      );

      if (response.data.success) {
        toast({
          title: "Success",
          description: "Operator attendance status updated successfully",
        });
      } else {
        throw new Error(response.data.message || "Failed to update operator attendance status");
      }
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to update operator attendance status";
      set({
        updateStatusError: errorMessage,
        isUpdatingStatus: false,
      });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  fetchStatusInfo: async () => {
    set({ isStatusInfoLoading: true, statusInfoError: null });

    try {
      const response = await api.get<StatusInfo[]>("/status-tracking/api/status-info");
      set({
        statusInfoList: response.data,
        isStatusInfoLoading: false,
      });
    } catch (error) {
      const errorMessage = getApiErrorMessage(error) || "Failed to fetch status information";
      set({
        statusInfoError: errorMessage,
        isStatusInfoLoading: false,
      });
    }
  },

  // SignalR methods
  connectToStatusMonitoring: async () => {
    try {
      await signalRService.connectToStatusMonitoring();
      console.log(`${new Date().toLocaleTimeString()} - Team leader store connected to status monitoring hub`);

      // Set up listeners after successful connection
      get().setupStatusMonitoringListeners();
    } catch (error) {
      const errorMessage = error && typeof error === 'object' && 'message' in error ? (error as Error).message : String(error);
      console.log(`${new Date().toLocaleTimeString()} - Team leader store failed to connect to status monitoring: ${errorMessage}`);

      // Show error toast
      toast({
        title: "Connection Error",
        description: "Failed to connect to status monitoring. Real-time updates may not work.",
        variant: "destructive",
      });
    }
  },

  disconnectFromStatusMonitoring: async () => {
    try {
      await signalRService.disconnectFromStatusMonitoring();
      signalRService.resetStatusMonitoringCallbacks();
      console.log(`${new Date().toLocaleTimeString()} - Team leader store disconnected from status monitoring`);
    } catch (error) {
      const errorMessage = error && typeof error === 'object' && 'message' in error ? (error as Error).message : String(error);
      console.log(`${new Date().toLocaleTimeString()} - Error disconnecting from status monitoring: ${errorMessage}`);
    }
  },

  joinStatusMonitoringGroups: async (teamId?: string, shiftInstantId?: string) => {
    const state = get();
    const currentTeamId = teamId || state.statusMonitoringData?.status_data?.[0]?.team_id;
    const currentShiftInstantId = shiftInstantId || state.statusMonitoringData?.shift_instant_id;

    try {
      if (currentTeamId) {
        await signalRService.joinTeamGroup(currentTeamId);
      }

      if (currentShiftInstantId) {
        await signalRService.joinShiftInstantGroup(currentShiftInstantId);
      }
    } catch (error) {
      const errorMessage = error && typeof error === 'object' && 'message' in error ? (error as Error).message : String(error);
      console.log(`${new Date().toLocaleTimeString()} - Error joining status monitoring groups: ${errorMessage}`);
    }
  },

  leaveStatusMonitoringGroups: async (teamId?: string, shiftInstantId?: string) => {
    const state = get();
    const currentTeamId = teamId || state.statusMonitoringData?.status_data?.[0]?.team_id;
    const currentShiftInstantId = shiftInstantId || state.statusMonitoringData?.shift_instant_id;

    try {
      if (currentTeamId) {
        await signalRService.leaveTeamGroup(currentTeamId);
      }

      if (currentShiftInstantId) {
        await signalRService.leaveShiftInstantGroup(currentShiftInstantId);
      }
    } catch (error) {
      const errorMessage = error && typeof error === 'object' && 'message' in error ? (error as Error).message : String(error);
      console.log(`${new Date().toLocaleTimeString()} - Error leaving status monitoring groups: ${errorMessage}`);
    }
  },

  setupStatusMonitoringListeners: () => {
    const state = get();

    // Listen for operator status updates
    signalRService.onOperatorStatusUpdate((update: OperatorStatusUpdate) => {
      console.log(`${new Date().toLocaleTimeString()} - OperatorStatusUpdate: ${JSON.stringify(update)}`);

      // Update the status monitoring data with the new status
      const currentData = get().statusMonitoringData;
      if (currentData?.status_data) {
        const updatedStatusData = currentData.status_data.map(operator => {
          if (operator.legacy_id === update.operatorId) {
            // Find today's date
            const today = new Date().toISOString().split('T')[0];

            // Update or add the daily record for today
            const updatedDailyRecords = operator.dailyRecords.map(record => {
              if (record.date === today) {
                // Add the new status code to today's record
                return {
                  ...record,
                  statusCodes: [
                    ...record.statusCodes,
                    {
                      code: update.statusCode,
                      startDateTime: update.timestamp,
                    }
                  ]
                };
              }
              return record;
            });

            // If no record for today exists, create one
            const hasToday = updatedDailyRecords.some(record => record.date === today);
            if (!hasToday) {
              updatedDailyRecords.push({
                date: today,
                statusCodes: [{
                  code: update.statusCode,
                  startDateTime: update.timestamp,
                }]
              });
            }

            return {
              ...operator,
              dailyRecords: updatedDailyRecords
            };
          }
          return operator;
        });

        set({
          statusMonitoringData: {
            ...currentData,
            status_data: updatedStatusData
          }
        });

        // Show toast notification
        toast({
          title: "Status Updated",
          description: `Operator ${update.operatorId} status changed to ${update.statusCode}`,
        });
      }
    });

    // Listen for shift instant status updates
    signalRService.onShiftInstantStatusUpdate((update: ShiftInstantStatusUpdate) => {
      console.log(`${new Date().toLocaleTimeString()} - ShiftInstantStatusUpdate: ${JSON.stringify(update)}`);

      const currentData = get().statusMonitoringData;
      if (currentData) {
        set({
          statusMonitoringData: {
            ...currentData,
            shift_status: update.currentStatus
          }
        });

        toast({
          title: "Shift Status Updated",
          description: `Shift status changed from ${update.previousStatus} to ${update.currentStatus}`,
        });
      }
    });

    // Listen for team status monitoring updates
    signalRService.onTeamStatusMonitoringUpdate((update: TeamStatusMonitoringUpdate) => {
      console.log(`${new Date().toLocaleTimeString()} - TeamStatusMonitoringUpdate: ${JSON.stringify(update)}`);

      // Refresh the status monitoring data
      if (state.statusMonitoringData) {
        const endDate = new Date().toISOString().split("T")[0];
        get().fetchStatusMonitoring(endDate);
      }

      toast({
        title: "Team Data Updated",
        description: "Team status monitoring data has been refreshed",
      });
    });
  },

  // Clear actions
  clearTeamData: () => set({ teamData: null, teamDataError: null }),
  clearCurrentStatus: () =>
    set({ currentStatusData: null, currentStatusError: null }),
  clearStatusMonitoring: () =>
    set({ statusMonitoringData: null, statusMonitoringError: null }),
  clearStatusHistory: () =>
    set({ statusHistory: {}, statusHistoryError: null }),
  clearErrors: () =>
    set({
      teamDataError: null,
      currentStatusError: null,
      statusMonitoringError: null,
      statusHistoryError: null,
      updateStatusError: null,
      statusInfoError: null,
    }),
}));
